#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import asyncio
from playwright.async_api import async_playwright
import re
import random

class SimpleFiller:
    def __init__(self, answers_file="answers.json"):
        """初始化表单填写器"""
        with open(answers_file, 'r', encoding='utf-8') as f:
            self.answers = json.load(f)
        print(f"已加载 {len(self.answers)} 道题目的答案")
    
    def find_answer(self, question_text):
        """根据题目文本查找答案"""
        # 清理题目文本
        clean_question = re.sub(r'\s+', '', question_text.strip())
        clean_question = re.sub(r'[（）()。，、？！]', '', clean_question)
        
        # 在答案库中查找匹配的题目
        for q, answer in self.answers.items():
            clean_q = re.sub(r'\s+', '', q.strip())
            clean_q = re.sub(r'[（）()。，、？！]', '', clean_q)
            
            # 检查是否包含关键词
            if clean_question in clean_q or clean_q in clean_question:
                return answer
            
            # 模糊匹配
            if len(clean_question) > 10 and len(clean_q) > 10:
                match_count = sum(1 for i in range(0, len(clean_question), 3) 
                                if clean_question[i:i+3] in clean_q)
                if match_count >= 3:
                    return answer
        
        return None
    
    async def fill_form(self, url):
        """自动填写表单"""
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False, slow_mo=500)
            context = await browser.new_context()
            page = await context.new_page()
            
            try:
                print(f"正在访问: {url}")
                await page.goto(url)
                await page.wait_for_load_state('networkidle')
                await asyncio.sleep(3)
                
                # 点击继续按钮
                continue_buttons = await page.query_selector_all('button, input[type="button"], .btn')
                for button in continue_buttons:
                    text = await button.inner_text()
                    if '继续' in text or '开始' in text:
                        print(f"点击按钮: {text}")
                        await button.click()
                        break
                
                # 等待页面加载
                await asyncio.sleep(5)
                print("开始填写题目...")
                
                # 连续填写题目
                await self.fill_questions_continuously(page)
                
                # 提交表单
                await self.submit_form(page)
                
            except Exception as e:
                print(f"发生错误: {e}")
                await page.screenshot(path="error.png")
            
            finally:
                await asyncio.sleep(5)  # 保持浏览器打开一会儿
                await browser.close()
    
    async def fill_questions_continuously(self, page):
        """连续填写题目"""
        question_count = 0
        max_questions = 100
        
        for i in range(1, max_questions + 1):
            print(f"\n=== 处理第 {i} 题 ===")
            
            # 尝试填写当前题目
            success = await self.fill_current_question(page, i)
            
            if success:
                question_count += 1
                print(f"第 {i} 题填写成功")
                
                # 滚动到下一题
                await page.evaluate("window.scrollBy(0, 200)")
                await asyncio.sleep(1)
                
            else:
                print(f"第 {i} 题未找到或填写失败")
                
                # 尝试滚动寻找下一题
                await page.evaluate("window.scrollBy(0, 300)")
                await asyncio.sleep(1)
                
                # 如果连续几题都找不到，可能已经到底了
                if i > 10 and question_count == 0:
                    print("可能没有找到题目，停止填写")
                    break
                elif i - question_count > 5:
                    print("连续多题未找到，可能已完成所有题目")
                    break
        
        print(f"总共填写了 {question_count} 道题目")
    
    async def fill_current_question(self, page, question_num):
        """填写当前可见的题目"""
        try:
            # 获取页面文本
            page_text = await page.evaluate('document.body.innerText')
            
            # 查找当前题目
            patterns = [
                rf'{question_num}[、．.]\s*单选题([^0-9]+?)(?=\d+[、．.]\s*[单多判]选题|$)',
                rf'{question_num}[、．.]\s*多选题([^0-9]+?)(?=\d+[、．.]\s*[单多判]选题|$)',
                rf'{question_num}[、．.]\s*判断题([^0-9]+?)(?=\d+[、．.]\s*[单多判]选题|$)',
            ]
            
            question_text = None
            question_type = "single"
            
            for pattern in patterns:
                match = re.search(pattern, page_text, re.DOTALL)
                if match:
                    question_text = match.group(0)
                    if "多选题" in question_text:
                        question_type = "multiple"
                    elif "判断题" in question_text:
                        question_type = "judge"
                    break
            
            if not question_text:
                return False
            
            print(f"找到题目: {question_text[:50]}...")
            
            # 查找答案
            answer = self.find_answer(question_text)
            if not answer:
                # 随机生成答案
                if question_type == "judge":
                    answer = random.choice(["A", "B"])
                elif question_type == "multiple":
                    answer = random.choice(["A", "B", "C", "D", "AB", "AC", "BC", "ABC"])
                else:
                    answer = random.choice(["A", "B", "C", "D"])
                print(f"未找到答案，随机选择: {answer}")
            else:
                print(f"找到答案: {answer}")
            
            # 填写答案
            return await self.fill_answer(page, answer, question_type)
            
        except Exception as e:
            print(f"处理题目时出错: {e}")
            return False
    
    async def fill_answer(self, page, answer, question_type):
        """填写答案"""
        try:
            # 查找所有可见的选项
            options = await page.query_selector_all('input[type="radio"], input[type="checkbox"]')
            visible_options = []
            
            for option in options:
                try:
                    if await option.is_visible():
                        visible_options.append(option)
                except:
                    continue
            
            if not visible_options:
                print("未找到可见选项")
                return False
            
            print(f"找到 {len(visible_options)} 个可见选项")
            
            # 根据题目类型填写
            if question_type == "judge":
                return await self.fill_judge_answer(visible_options, answer)
            elif question_type == "multiple":
                return await self.fill_multiple_answer(visible_options, answer)
            else:
                return await self.fill_single_answer(visible_options, answer)
                
        except Exception as e:
            print(f"填写答案时出错: {e}")
            return False
    
    async def fill_single_answer(self, options, answer):
        """填写单选题"""
        # 尝试根据答案字母选择
        for i, option in enumerate(options[:4]):  # 假设最多4个选项
            option_letter = chr(65 + i)  # A, B, C, D
            if option_letter == answer:
                try:
                    await option.click()
                    print(f"选择了选项 {option_letter}")
                    return True
                except:
                    continue
        return False
    
    async def fill_multiple_answer(self, options, answer):
        """填写多选题"""
        selected = 0
        for i, option in enumerate(options[:4]):
            option_letter = chr(65 + i)  # A, B, C, D
            if option_letter in answer:
                try:
                    await option.click()
                    selected += 1
                    print(f"选择了选项 {option_letter}")
                except:
                    continue
        return selected > 0
    
    async def fill_judge_answer(self, options, answer):
        """填写判断题"""
        # A=正确, B=错误
        target_index = 0 if answer == "A" else 1
        if len(options) > target_index:
            try:
                await options[target_index].click()
                print(f"选择了 {'正确' if answer == 'A' else '错误'}")
                return True
            except:
                pass
        return False
    
    async def submit_form(self, page):
        """提交表单"""
        print("查找提交按钮...")
        
        # 滚动到页面底部
        await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
        await asyncio.sleep(2)
        
        # 查找提交按钮
        submit_buttons = await page.query_selector_all('button, input[type="submit"], input[type="button"]')
        
        for button in submit_buttons:
            try:
                text = await button.inner_text()
                if any(keyword in text for keyword in ["交卷", "提交", "完成", "确认"]):
                    print(f"找到提交按钮: {text}")
                    await button.click()
                    print("表单已提交!")
                    return True
            except:
                continue
        
        print("未找到提交按钮")
        return False

async def main():
    url = ("http://wap.xiaoyuananquantong.com/guns-vip-main/wap/newStudentArticle"
           "?id=1493144438782836737&userId=1959537042895429633&ah=")
    
    filler = SimpleFiller()
    await filler.fill_form(url)

if __name__ == "__main__":
    asyncio.run(main())
