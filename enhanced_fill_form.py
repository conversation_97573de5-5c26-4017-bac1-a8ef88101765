#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import asyncio
from playwright.async_api import async_playwright
import re
import random
from loguru import logger
import sys

# 配置日志
logger.remove()
logger.add(
    sys.stdout,
    format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{function}</cyan> | {message}",
)
logger.add(
    "form_fill.log",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {function} | {message}",
)


class EnhancedFormFiller:
    def __init__(self, answers_file="answers.json"):
        """初始化表单填写器"""
        with open(answers_file, "r", encoding="utf-8") as f:
            self.answers = json.load(f)
        logger.info(f"已加载 {len(self.answers)} 道题目的答案")

    def find_answer(self, question_text):
        """根据题目文本查找答案"""
        logger.debug(f"查找答案: {question_text[:50]}...")

        # 清理题目文本，移除多余的空格和标点
        clean_question = re.sub(r"\s+", "", question_text.strip())
        clean_question = re.sub(r"[（）()。，、？！]", "", clean_question)

        # 在答案库中查找匹配的题目
        for q, answer in self.answers.items():
            clean_q = re.sub(r"\s+", "", q.strip())
            clean_q = re.sub(r"[（）()。，、？！]", "", clean_q)

            # 检查是否包含关键词
            if clean_question in clean_q or clean_q in clean_question:
                logger.success(f"找到匹配答案: {answer}")
                return answer

            # 模糊匹配：检查主要关键词
            if len(clean_question) > 20 and len(clean_q) > 20:
                # 计算相似度
                common_chars = sum(
                    1
                    for i in range(0, min(len(clean_question), len(clean_q)), 2)
                    if i < len(clean_question)
                    and i < len(clean_q)
                    and clean_question[i : i + 2] in clean_q
                )
                similarity = common_chars / max(len(clean_question), len(clean_q)) * 100

                if similarity > 30:  # 30% 相似度
                    logger.success(
                        f"模糊匹配找到答案: {answer} (相似度: {similarity:.1f}%)"
                    )
                    return answer

        logger.warning("未找到匹配的答案")
        return None

    async def fill_form(self, url):
        """自动填写表单"""
        async with async_playwright() as p:
            # 启动浏览器
            browser = await p.chromium.launch(headless=False, slow_mo=1000)
            context = await browser.new_context()
            page = await context.new_page()

            try:
                logger.info(f"正在访问: {url}")
                await page.goto(url)
                await page.wait_for_load_state("networkidle")

                # 等待页面加载完成
                await asyncio.sleep(3)
                logger.info("页面加载完成")

                # 查找并点击"继续"按钮
                await self.click_continue_button(page)

                # 等待题目页面加载
                await asyncio.sleep(5)
                await page.wait_for_load_state("networkidle")
                logger.info("题目页面加载完成")

                # 获取页面内容进行调试
                await self.debug_page_content(page)

                # 查找所有题目并填写
                await self.find_and_fill_all_questions(page)

                # 查找并点击提交按钮
                submit_success = await self.submit_form(page)
                if submit_success:
                    logger.success("表单提交成功！")
                else:
                    logger.error("表单提交失败")

                # 等待提交完成
                await asyncio.sleep(5)
                logger.info("任务完成！")

            except Exception as e:
                logger.error(f"发生错误: {e}")
                await page.screenshot(path="error_screenshot.png")
                logger.info("已保存错误截图: error_screenshot.png")

            finally:
                await asyncio.sleep(3)  # 保持浏览器打开一会儿
                await browser.close()

    async def click_continue_button(self, page):
        """点击继续按钮"""
        logger.info("查找继续按钮...")

        continue_buttons = await page.query_selector_all(
            'button, input[type="button"], input[type="submit"], .btn, [onclick]'
        )
        logger.debug(f"找到 {len(continue_buttons)} 个可能的按钮")

        for i, button in enumerate(continue_buttons):
            try:
                text = await button.inner_text()
                logger.debug(f"按钮 {i + 1}: {text}")

                if any(keyword in text for keyword in ["继续", "开始", "进入", "测试"]):
                    logger.success(f"找到继续按钮: {text}")
                    await button.click()
                    logger.info("已点击继续按钮")
                    return True
            except Exception as e:
                logger.debug(f"检查按钮 {i + 1} 时出错: {e}")
                continue

        logger.warning("未找到继续按钮")
        return False

    async def debug_page_content(self, page):
        """调试页面内容"""
        try:
            page_text = await page.evaluate("document.body.innerText")
            logger.info(f"页面文本长度: {len(page_text)}")

            # 查找题目模式
            question_patterns = [
                r"\d+[、．.]\s*单选题",
                r"\d+[、．.]\s*多选题",
                r"\d+[、．.]\s*判断题",
            ]

            total_questions = 0
            for pattern in question_patterns:
                matches = re.findall(pattern, page_text)
                total_questions += len(matches)
                if matches:
                    logger.info(f"找到 {len(matches)} 道题目匹配模式: {pattern}")
                    logger.debug(f"题目示例: {matches[:3]}")

            logger.info(f"页面总共可能有 {total_questions} 道题目")

            # 显示页面文本的前500字符
            logger.debug(f"页面文本预览:\n{page_text[:500]}...")

        except Exception as e:
            logger.error(f"调试页面内容时出错: {e}")

    async def find_and_fill_all_questions(self, page):
        """查找并填写所有题目"""
        logger.info("开始查找并填写题目...")

        # 获取页面文本
        page_text = await page.evaluate("document.body.innerText")

        # 使用正则表达式直接从页面文本中提取题目
        question_pattern = (
            r"(\d+)[、．.]\s*([单多判]选题[^0-9]*?)(?=\d+[、．.]\s*[单多判]选题|$)"
        )
        text_questions = re.findall(question_pattern, page_text, re.DOTALL)

        logger.info(f"从页面文本中提取到 {len(text_questions)} 道题目")

        if not text_questions:
            logger.warning("未找到任何题目，尝试其他方法...")
            await self.fallback_question_search(page)
            return

        # 处理每道题目
        filled_count = 0
        for i, (question_num, question_content) in enumerate(text_questions, 1):
            logger.info(f"\n=== 处理第 {question_num} 题 ===")

            question_text = f"{question_num}、{question_content}"
            question_type = self.get_question_type(question_content)

            logger.info(f"题目类型: {question_type}")
            logger.debug(f"题目内容: {question_text[:100]}...")

            # 查找答案
            answer = self.find_answer(question_text)
            if not answer:
                # 随机生成答案
                answer = self.generate_random_answer(question_type)
                logger.warning(f"未找到答案，随机选择: {answer}")

            # 填写题目
            success = await self.fill_question_by_number(
                page, int(question_num), answer, question_type
            )

            if success:
                filled_count += 1
                logger.success(f"第 {question_num} 题填写成功")
            else:
                logger.error(f"第 {question_num} 题填写失败")

            # 滚动到下一题
            await page.evaluate("window.scrollBy(0, 200)")
            await asyncio.sleep(1)

        logger.success(f"总共成功填写了 {filled_count}/{len(text_questions)} 道题目")

    def get_question_type(self, question_content):
        """获取题目类型"""
        if "多选题" in question_content:
            return "multiple"
        elif "判断题" in question_content:
            return "judge"
        else:
            return "single"

    def generate_random_answer(self, question_type):
        """生成随机答案"""
        if question_type == "judge":
            return random.choice(["A", "B"])
        elif question_type == "multiple":
            return random.choice(
                [
                    "A",
                    "B",
                    "C",
                    "D",
                    "AB",
                    "AC",
                    "AD",
                    "BC",
                    "BD",
                    "CD",
                    "ABC",
                    "ABD",
                    "ACD",
                    "BCD",
                    "ABCD",
                ]
            )
        else:  # single
            return random.choice(["A", "B", "C", "D"])

    async def fallback_question_search(self, page):
        """备用的题目查找方法"""
        logger.info("使用备用方法查找题目...")

        # 查找所有可能包含题目的元素
        elements = await page.query_selector_all("div, p, span")
        logger.debug(f"找到 {len(elements)} 个元素")

        question_elements = []
        for element in elements:
            try:
                text = await element.inner_text()
                if text and re.search(r"\d+[、．.]\s*[单多判]选题", text):
                    question_elements.append(element)
                    logger.debug(f"找到题目元素: {text[:50]}...")
            except:
                continue

        logger.info(f"通过备用方法找到 {len(question_elements)} 个题目元素")

    async def fill_question_by_number(self, page, question_num, answer, question_type):
        """通过题目编号填写题目"""
        logger.debug(f"尝试填写第 {question_num} 题，答案: {answer}")

        # 查找所有可能的选项元素 - 根据页面实际结构调整
        selectors = [
            'input[type="radio"]',
            'input[type="checkbox"]',
            "label",
            # 根据页面文本，选项可能是简单的文本，尝试查找包含A、B、C、D的元素
            '*:has-text("A正确")',
            '*:has-text("B错误")',
            '*:has-text("A")',
            '*:has-text("B")',
            '*:has-text("C")',
            '*:has-text("D")',
            # 尝试查找包含选项文本的元素
            'div:has-text("正确")',
            'div:has-text("错误")',
            'span:has-text("正确")',
            'span:has-text("错误")',
            # 通用选择器
            "div[onclick]",
            "span[onclick]",
            "button",
            "[data-value]",
            ".option",
            ".choice",
            '[class*="option"]',
            '[class*="choice"]',
            '[class*="answer"]',
        ]

        all_options = []
        for selector in selectors:
            elements = await page.query_selector_all(selector)
            all_options.extend(elements)
            if elements:
                logger.debug(f"通过选择器 '{selector}' 找到 {len(elements)} 个元素")

        # 去重
        unique_options = []
        seen_elements = set()
        for option in all_options:
            element_id = id(option)
            if element_id not in seen_elements:
                unique_options.append(option)
                seen_elements.add(element_id)

        logger.debug(f"总共找到 {len(unique_options)} 个唯一选项元素")

        # 尝试找到与此题目相关的选项
        question_options = []
        for option in unique_options:
            try:
                # 检查name属性是否包含题目编号
                name_attr = await option.get_attribute("name")
                if name_attr and str(question_num) in name_attr:
                    question_options.append(option)
                    logger.debug(f"通过name属性找到选项: {name_attr}")
                    continue

                # 检查value属性
                value_attr = await option.get_attribute("value")
                if value_attr and str(question_num) in value_attr:
                    question_options.append(option)
                    logger.debug(f"通过value属性找到选项: {value_attr}")
                    continue

                # 检查相邻的文本内容
                option_text = await option.inner_text()
                if option_text and (
                    f"{question_num}、" in option_text
                    or f"{question_num}." in option_text
                ):
                    question_options.append(option)
                    logger.debug(f"通过文本找到选项: {option_text[:30]}...")

            except Exception as e:
                logger.debug(f"检查选项时出错: {e}")
                continue

        if not question_options:
            # 如果没找到特定题目的选项，尝试查找当前可见的选项
            logger.warning(
                f"未找到题目 {question_num} 的特定选项，尝试查找当前可见选项"
            )

            # 获取当前视窗内的选项
            visible_options = []
            for option in unique_options:
                try:
                    is_visible = await option.is_visible()
                    if is_visible:
                        visible_options.append(option)
                except Exception:
                    continue

            # 如果有可见选项，使用前几个
            if visible_options:
                question_options = visible_options[:4]  # 假设每题最多4个选项
                logger.info(f"使用 {len(question_options)} 个可见选项")

        if not question_options:
            logger.error(f"第 {question_num} 题未找到任何选项")
            return False

        # 根据答案类型填写
        logger.info(
            f"开始填写第 {question_num} 题，类型: {question_type}，答案: {answer}"
        )

        if question_type == "judge":
            return await self.fill_judge_options(page, question_options, answer)
        elif question_type == "multiple":
            return await self.fill_multiple_options(page, question_options, answer)
        else:
            return await self.fill_single_options(page, question_options, answer)

    async def fill_single_options(self, page, options, answer):
        """填写单选题"""
        logger.debug(f"填写单选题，答案: {answer}")

        # 调试：先查看所有选项的内容
        logger.debug("=== 调试选项内容 ===")
        for i, option in enumerate(options):
            try:
                option_text = await option.inner_text()
                tag_name = await option.evaluate("element => element.tagName")
                class_name = await option.get_attribute("class") or ""
                logger.debug(
                    f"选项 {i + 1}: 标签={tag_name}, 类名={class_name}, 文本='{option_text}'"
                )
            except Exception as e:
                logger.debug(f"选项 {i + 1}: 获取信息失败 - {e}")

        # 改进的过滤逻辑：只过滤明确的提交按钮
        valid_options = []
        for option in options:
            try:
                option_text = await option.inner_text()
                # 只过滤明确包含提交相关文本的元素
                if option_text and option_text.strip() in [
                    "交卷",
                    "提交",
                    "完成",
                    "确认提交",
                    "结束",
                ]:
                    logger.debug(f"过滤掉提交按钮: {option_text}")
                    continue
                else:
                    valid_options.append(option)
                    logger.debug(f"保留选项: {option_text[:50]}...")
            except Exception:
                # 如果无法获取文本，保留该选项
                valid_options.append(option)
                logger.debug("保留无法获取文本的选项")

        logger.debug(f"过滤后有 {len(valid_options)} 个有效选项")

        # 如果过滤后没有选项，使用原始选项列表
        if len(valid_options) == 0:
            logger.warning("过滤后没有选项，使用原始选项列表")
            valid_options = options

        for i, option in enumerate(valid_options):
            try:
                option_text = await option.inner_text()
                option_letter = chr(65 + i)  # A, B, C, D

                logger.debug(f"选项 {option_letter}: {option_text[:30]}...")

                if option_text.strip().startswith(answer) or option_letter == answer:
                    # 先尝试关闭可能的遮罩层
                    await self.close_modal_if_exists(page)

                    # 使用JavaScript点击，避免遮罩层问题
                    await option.evaluate("element => element.click()")
                    logger.success(f"已选择选项 {option_letter}: {option_text[:30]}...")
                    return True
            except Exception as e:
                logger.debug(f"点击选项时出错: {e}")
                continue

        logger.warning("单选题填写失败")
        return False

    async def close_modal_if_exists(self, page):
        """关闭可能存在的遮罩层"""
        try:
            # 查找并关闭遮罩层
            modal_selectors = [
                "#layui-layer-shade1",
                ".layui-layer-shade",
                ".modal-backdrop",
                ".overlay",
            ]

            for selector in modal_selectors:
                modal = await page.query_selector(selector)
                if modal:
                    logger.debug(f"发现遮罩层: {selector}")
                    await modal.evaluate("element => element.remove()")
                    logger.debug("已移除遮罩层")
                    break
        except Exception as e:
            logger.debug(f"关闭遮罩层时出错: {e}")

    async def fill_multiple_options(self, options, answer):
        """填写多选题"""
        logger.debug(f"填写多选题，答案: {answer}")

        # 改进的过滤逻辑：只过滤明确的提交按钮
        valid_options = []
        for option in options:
            try:
                option_text = await option.inner_text()
                # 只过滤明确包含提交相关文本的元素
                if option_text and option_text.strip() in [
                    "交卷",
                    "提交",
                    "完成",
                    "确认提交",
                    "结束",
                ]:
                    logger.debug(f"过滤掉提交按钮: {option_text}")
                    continue
                else:
                    valid_options.append(option)
            except Exception:
                # 如果无法获取文本，保留该选项
                valid_options.append(option)

        # 如果过滤后没有选项，使用原始选项列表
        if len(valid_options) == 0:
            logger.warning("过滤后没有选项，使用原始选项列表")
            valid_options = options

        selected_count = 0
        for i, option in enumerate(valid_options):
            try:
                option_text = await option.inner_text()
                option_letter = chr(65 + i)  # A, B, C, D

                logger.debug(f"选项 {option_letter}: {option_text[:30]}...")

                if option_letter in answer:
                    # 先尝试关闭可能的遮罩层
                    await self.close_modal_if_exists(option.page)

                    # 使用JavaScript点击，避免遮罩层问题
                    await option.evaluate("element => element.click()")
                    selected_count += 1
                    logger.success(f"已选择选项 {option_letter}: {option_text[:30]}...")
            except Exception as e:
                logger.debug(f"点击选项时出错: {e}")
                continue

        if selected_count > 0:
            logger.success(f"多选题填写成功，选择了 {selected_count} 个选项")
            return True
        else:
            logger.warning("多选题填写失败")
            return False

    async def fill_judge_options(self, options, answer):
        """填写判断题"""
        logger.debug(f"填写判断题，答案: {answer}")

        # 改进的过滤逻辑：只过滤明确的提交按钮
        valid_options = []
        for option in options:
            try:
                option_text = await option.inner_text()
                # 只过滤明确包含提交相关文本的元素
                if option_text and option_text.strip() in [
                    "交卷",
                    "提交",
                    "完成",
                    "确认提交",
                    "结束",
                ]:
                    logger.debug(f"过滤掉提交按钮: {option_text}")
                    continue
                else:
                    valid_options.append(option)
            except Exception:
                # 如果无法获取文本，保留该选项
                valid_options.append(option)

        # 如果过滤后没有选项，使用原始选项列表
        if len(valid_options) == 0:
            logger.warning("过滤后没有选项，使用原始选项列表")
            valid_options = options

        target_text = "正确" if answer == "A" else "错误"
        target_index = 0 if answer == "A" else 1

        # 方法1: 根据文本内容选择
        for option in valid_options:
            try:
                option_text = await option.inner_text()
                logger.debug(f"判断题选项: {option_text}")

                if target_text in option_text:
                    # 先尝试关闭可能的遮罩层
                    await self.close_modal_if_exists(option.page)

                    # 使用JavaScript点击，避免遮罩层问题
                    await option.evaluate("element => element.click()")
                    logger.success(f"已选择: {option_text}")
                    return True
            except Exception as e:
                logger.debug(f"检查判断题选项时出错: {e}")
                continue

        # 方法2: 根据位置选择（第一个是正确，第二个是错误）
        if len(valid_options) > target_index:
            try:
                # 先尝试关闭可能的遮罩层
                await self.close_modal_if_exists(valid_options[target_index].page)

                # 使用JavaScript点击，避免遮罩层问题
                await valid_options[target_index].evaluate("element => element.click()")
                logger.success(
                    f"已选择判断题答案: {'正确' if answer == 'A' else '错误'}"
                )
                return True
            except Exception as e:
                logger.debug(f"按位置选择判断题时出错: {e}")

        logger.warning("判断题填写失败")
        return False

    async def submit_form(self, page):
        """提交表单"""
        logger.info("开始查找提交按钮...")

        # 滚动到页面底部
        await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
        await asyncio.sleep(2)
        logger.info("已滚动到页面底部")

        # 查找提交按钮
        all_buttons = await page.query_selector_all(
            'button, input[type="submit"], input[type="button"], [onclick]'
        )
        logger.debug(f"找到 {len(all_buttons)} 个可能的提交按钮")

        submit_keywords = ["交卷", "提交", "完成", "确认提交", "结束", "submit"]

        for i, button in enumerate(all_buttons):
            try:
                text = await button.inner_text()
                logger.debug(f"按钮 {i + 1}: {text}")

                if any(keyword in text for keyword in submit_keywords):
                    logger.success(f"找到提交按钮: {text}")
                    await button.click()
                    logger.info("已点击提交按钮")
                    return True
            except Exception as e:
                logger.debug(f"检查提交按钮时出错: {e}")
                continue

        logger.error("未找到提交按钮")
        return False


async def main():
    """主函数"""
    url = (
        "http://wap.xiaoyuananquantong.com/guns-vip-main/wap/newStudentArticle"
        "?id=1493144438782836737&userId=1959537042895429633&ah="
    )

    filler = EnhancedFormFiller()
    await filler.fill_form(url)


if __name__ == "__main__":
    logger.info("启动增强版表单填写器...")
    asyncio.run(main())
