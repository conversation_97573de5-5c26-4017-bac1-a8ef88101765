#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import asyncio
from playwright.async_api import async_playwright
import re
import random


class FormFiller:
    def __init__(self, answers_file="answers.json"):
        """初始化表单填写器"""
        with open(answers_file, "r", encoding="utf-8") as f:
            self.answers = json.load(f)
        print(f"已加载 {len(self.answers)} 道题目的答案")

    def find_answer(self, question_text):
        """根据题目文本查找答案"""
        # 清理题目文本，移除多余的空格和标点
        clean_question = re.sub(r"\s+", "", question_text.strip())
        clean_question = re.sub(r"[（）()。，、？！]", "", clean_question)

        # 在答案库中查找匹配的题目
        for q, answer in self.answers.items():
            clean_q = re.sub(r"\s+", "", q.strip())
            clean_q = re.sub(r"[（）()。，、？！]", "", clean_q)

            # 检查是否包含关键词
            if clean_question in clean_q or clean_q in clean_question:
                return answer

            # 模糊匹配：检查主要关键词
            question_keywords = [word for word in clean_question if len(word) > 1]
            q_keywords = [word for word in clean_q if len(word) > 1]

            # 如果有足够多的关键词匹配，认为是同一题目
            if len(question_keywords) > 3 and len(q_keywords) > 3:
                match_count = sum(1 for kw in question_keywords if kw in clean_q)
                if match_count >= min(3, len(question_keywords) * 0.6):
                    return answer

        return None

    async def fill_form(self, url):
        """自动填写表单"""
        async with async_playwright() as p:
            # 启动浏览器
            browser = await p.chromium.launch(headless=False, slow_mo=1000)
            context = await browser.new_context()
            page = await context.new_page()

            try:
                print(f"正在访问: {url}")
                await page.goto(url)
                await page.wait_for_load_state("networkidle")

                # 等待页面加载完成
                await asyncio.sleep(3)

                # 查找并点击"继续"按钮
                continue_buttons = await page.query_selector_all(
                    'button, input[type="button"], input[type="submit"], .btn, [onclick]'
                )
                for button in continue_buttons:
                    text = await button.inner_text()
                    if "继续" in text or "开始" in text or "进入" in text:
                        print(f"找到继续按钮: {text}")
                        await button.click()
                        break

                # 等待题目页面加载
                print("等待题目页面加载...")
                await asyncio.sleep(5)
                await page.wait_for_load_state("networkidle")

                # 检查页面内容
                page_content = await page.content()
                print(f"页面加载完成，内容长度: {len(page_content)}")

                # 获取页面文本查看是否有题目
                page_text = await page.evaluate("document.body.innerText")
                print(f"页面文本长度: {len(page_text)}")
                print(f"页面文本预览: {page_text[:500]}...")

                # 动态查找并填写题目
                await self.fill_all_questions_dynamically(page)

                # 查找并点击提交按钮
                submit_success = await self.submit_form(page)
                if submit_success:
                    print("表单提交成功！")
                else:
                    print("未找到提交按钮，请手动提交")

                # 等待提交完成
                await asyncio.sleep(5)
                print("表单填写完成！")

            except Exception as e:
                print(f"发生错误: {e}")
                await page.screenshot(path="error_screenshot.png")

            finally:
                await browser.close()

    async def fill_all_questions_dynamically(self, page):
        """动态查找并填写所有题目"""
        question_num = 1
        max_questions = 100  # 设置最大题目数量防止无限循环
        filled_questions = set()  # 记录已填写的题目

        while question_num <= max_questions:
            print(f"\n=== 尝试处理第 {question_num} 题 ===")

            # 查找当前题目
            current_question = await self.find_current_question(page, question_num)

            if not current_question:
                print(f"未找到第 {question_num} 题，尝试滚动查找...")

                # 滚动页面寻找下一题
                await page.evaluate("window.scrollBy(0, 300)")
                await asyncio.sleep(1)

                # 再次尝试查找
                current_question = await self.find_current_question(page, question_num)

                if not current_question:
                    print(f"滚动后仍未找到第 {question_num} 题，可能已到达最后一题")
                    break

            # 检查是否已经填写过这道题
            question_key = f"{question_num}_{current_question['text'][:50]}"
            if question_key in filled_questions:
                print(f"第 {question_num} 题已填写过，跳过")
                question_num += 1
                continue

            # 填写题目
            await self.fill_question(page, current_question, question_num)
            filled_questions.add(question_key)

            # 滚动到下一题
            await self.scroll_to_next_question(page, question_num)
            await asyncio.sleep(2)

            question_num += 1

        print(f"完成填写，共处理了 {len(filled_questions)} 道题目")

    async def find_current_question(self, page, question_num):
        """查找指定编号的题目"""
        # 获取当前页面文本
        page_text = await page.evaluate("document.body.innerText")

        # 查找指定编号的题目
        patterns = [
            rf"{question_num}[、．.]\s*单选题([^0-9]+?)(?={question_num + 1}[、．.]\s*[单多判]选题|$)",
            rf"{question_num}[、．.]\s*多选题([^0-9]+?)(?={question_num + 1}[、．.]\s*[单多判]选题|$)",
            rf"{question_num}[、．.]\s*判断题([^0-9]+?)(?={question_num + 1}[、．.]\s*[单多判]选题|$)",
        ]

        for pattern in patterns:
            match = re.search(pattern, page_text, re.DOTALL)
            if match:
                question_text = f"{question_num}、{match.group(0)}"
                question_type = "single"
                if "多选题" in match.group(0):
                    question_type = "multiple"
                elif "判断题" in match.group(0):
                    question_type = "judge"

                return {
                    "text": question_text.strip(),
                    "type": question_type,
                    "element": None,
                    "is_text_question": True,
                }

        return None

    async def find_questions(self, page):
        """查找页面上的所有题目"""
        questions = []

        # 等待页面完全加载
        await asyncio.sleep(3)

        # 首先尝试获取页面的所有文本内容
        page_content = await page.content()
        print("页面内容预览:")
        print(page_content[:1000])

        # 首先尝试获取页面的所有文本，查看是否包含题目
        page_text = await page.evaluate("document.body.innerText")
        print(f"页面文本长度: {len(page_text)}")

        # 使用正则表达式直接从页面文本中提取题目
        question_pattern = (
            r"(\d+[、．.]\s*[单多判]选题[^A-D]*?)(?=\d+[、．.]\s*[单多判]选题|$)"
        )
        text_questions = re.findall(question_pattern, page_text, re.DOTALL)
        print(f"从文本中找到 {len(text_questions)} 道题目")

        # 如果从文本中找到了题目，直接处理
        if text_questions:
            for i, question_text in enumerate(text_questions, 1):
                questions.append(
                    {
                        "element": None,  # 文本题目没有对应的DOM元素
                        "text": question_text.strip(),
                        "type": await self.get_question_type(question_text),
                        "is_text_question": True,
                    }
                )
                print(f"文本题目 {i}: {question_text[:50]}...")
            return questions

        # 如果没有从文本中找到，继续使用DOM查找
        selectors = [
            ".question",
            ".item",
            '[class*="question"]',
            '[class*="item"]',
            'div:has-text("选题")',
            'div:has-text("判断题")',
            'div:has-text("单选题")',
            'div:has-text("多选题")',
            "div",
            "p",
            "span",
        ]

        elements = []
        for selector in selectors:
            temp_elements = await page.query_selector_all(selector)
            if temp_elements:
                print(f"使用选择器 {selector} 找到 {len(temp_elements)} 个元素")
                elements.extend(temp_elements)
                if len(elements) > 100:  # 增加元素数量限制
                    break

        # 检查每个元素是否包含题目，并去重
        seen_questions = set()
        for element in elements:
            try:
                question_text = await element.inner_text()
                if question_text and await self.is_question(question_text):
                    # 使用题目的前100个字符作为去重标识
                    question_key = question_text[:100].strip()
                    if question_key not in seen_questions:
                        seen_questions.add(question_key)
                        questions.append(
                            {
                                "element": element,
                                "text": question_text,
                                "type": await self.get_question_type(question_text),
                            }
                        )
                        print(f"找到题目: {question_text[:50]}...")
            except Exception:
                continue

        return questions

    async def contains_question_pattern(self, element):
        """检查元素是否包含题目模式"""
        text = await element.inner_text()
        patterns = [
            r"\d+[、．.]\s*[单多判]选题",
            r"\d+[、．.]\s*判断题",
            r"[单多判]选题.*[？?]",
            r"\d+[、．.].*[？?]",
        ]
        return any(re.search(pattern, text) for pattern in patterns)

    async def is_question(self, text):
        """判断文本是否为题目"""
        if len(text.strip()) < 10:
            return False

        # 检查是否包含题目编号和题目类型
        question_patterns = [
            r"\d+[、．.]\s*单选题",
            r"\d+[、．.]\s*多选题",
            r"\d+[、．.]\s*判断题",
        ]

        for pattern in question_patterns:
            if re.search(pattern, text):
                return True

        # 检查是否包含题目内容关键词
        content_indicators = [
            "虚拟物品交易诈骗",
            "银行卡在ATM机上取款",
            "校园内的自动售货机",
            "短信验证码",
            "小王在某游戏论坛",
            "当你使用社交媒体时",
            "接到自称警察的电话",
            "小黄同学在网上看到",
            "网上遇到异性主动添加",
            "一名自称师姐的女生",
        ]

        return any(indicator in text for indicator in content_indicators)

    async def get_question_type(self, text):
        """获取题目类型"""
        if "多选题" in text:
            return "multiple"
        elif "单选题" in text:
            return "single"
        elif "判断题" in text:
            return "judge"
        else:
            return "single"  # 默认单选

    async def fill_question(self, page, question_info, question_num):
        """填写单个题目"""
        question_text = question_info["text"]
        question_type = question_info["type"]

        print(f"\n第 {question_num} 题:")
        print(f"类型: {question_type}")
        print(f"题目: {question_text[:100]}...")

        # 查找答案
        answer = self.find_answer(question_text)
        if not answer:
            # 如果没有找到答案，随机生成一个
            if question_type == "judge":
                answer = random.choice(["A", "B"])
            elif question_type == "multiple":
                answer = random.choice(
                    [
                        "A",
                        "B",
                        "C",
                        "D",
                        "AB",
                        "AC",
                        "AD",
                        "BC",
                        "BD",
                        "CD",
                        "ABC",
                        "ABD",
                        "ACD",
                        "BCD",
                        "ABCD",
                    ]
                )
            else:  # single
                answer = random.choice(["A", "B", "C", "D"])
            print(f"未找到答案，随机选择: {answer}")
        else:
            print(f"找到答案: {answer}")

        # 尝试填写答案
        success = False

        # 如果是文本题目（没有DOM元素），需要通过其他方式定位选项
        if question_info.get("is_text_question", False):
            success = await self.fill_text_question(
                page, question_info, answer, question_num
            )
        else:
            # 根据题目类型填写答案
            if question_type == "judge":
                success = await self.fill_judge_question(page, question_info, answer)
            elif question_type == "multiple":
                success = await self.fill_multiple_question(page, question_info, answer)
            else:
                success = await self.fill_single_question(page, question_info, answer)

        # 如果通过DOM元素填写失败，尝试通过页面上的所有选项填写
        if not success:
            print(f"DOM方式填写失败，尝试通用方式填写题目 {question_num}")
            success = await self.fill_question_by_number(
                page, question_num, answer, question_type
            )

        if not success:
            print(f"题目 {question_num} 填写失败")

    async def scroll_to_next_question(self, page, current_question_num):
        """滚动到下一题"""
        try:
            # 方法1: 尝试滚动页面
            await page.evaluate("window.scrollBy(0, 300)")
            await asyncio.sleep(0.5)

            # 方法2: 尝试查找下一题并滚动到该位置
            next_question_num = current_question_num + 1
            next_question_selectors = [
                f'*:has-text("{next_question_num}、")',
                f'*:has-text("{next_question_num}.")',
                f'div:has-text("{next_question_num}")',
            ]

            for selector in next_question_selectors:
                try:
                    next_element = await page.query_selector(selector)
                    if next_element:
                        await next_element.scroll_into_view_if_needed()
                        print(f"滚动到第 {next_question_num} 题")
                        return
                except Exception:
                    continue

            # 方法3: 如果找不到下一题，继续向下滚动
            await page.evaluate("window.scrollBy(0, 200)")
            print(f"完成第 {current_question_num} 题，继续滚动")

        except Exception as e:
            print(f"滚动时出错: {e}")

    async def fill_text_question(self, page, question_info, answer, question_num):
        """填写文本题目（通过题目编号定位选项）"""
        print(f"处理文本题目 {question_num}，答案: {answer}")

        # 尝试通过题目编号找到对应的选项
        # 从页面文本中提取题目编号
        question_text = question_info["text"]
        question_match = re.search(r"(\d+)[、．.]", question_text)
        if not question_match:
            print("无法提取题目编号")
            return

        question_number = question_match.group(1)
        print(f"题目编号: {question_number}")

        # 查找所有可能的选项元素
        all_options = await page.query_selector_all(
            'input[type="radio"], input[type="checkbox"], label'
        )

        # 尝试找到与此题目相关的选项
        for option in all_options:
            try:
                # 检查选项的name属性或附近的文本
                name_attr = await option.get_attribute("name")
                if name_attr and question_number in name_attr:
                    option_text = await option.inner_text()
                    if not option_text:
                        # 如果是input元素，查找相邻的label
                        label = await page.query_selector(
                            f'label[for="{await option.get_attribute("id")}"]'
                        )
                        if label:
                            option_text = await label.inner_text()

                    if option_text and option_text.strip().startswith(answer):
                        await option.click()
                        print(f"已选择选项: {option_text.strip()}")
                        return True
            except Exception:
                continue

        print(f"未找到题目 {question_number} 的选项")
        return False

    async def fill_question_by_number(self, page, question_num, answer, question_type):
        """通过题目编号在页面上查找并填写选项"""
        print(f"尝试通过题目编号 {question_num} 填写答案 {answer}")

        # 查找所有可能的选项元素
        all_options = await page.query_selector_all(
            'input[type="radio"], input[type="checkbox"], label'
        )

        # 尝试找到与此题目相关的选项
        question_options = []
        for option in all_options:
            try:
                # 检查name属性是否包含题目编号
                name_attr = await option.get_attribute("name")
                if name_attr and str(question_num) in name_attr:
                    question_options.append(option)
                    continue

                # 检查value属性
                value_attr = await option.get_attribute("value")
                if value_attr and str(question_num) in value_attr:
                    question_options.append(option)
                    continue

                # 检查相邻的文本内容
                option_text = await option.inner_text()
                if option_text and (
                    f"{question_num}、" in option_text
                    or f"{question_num}." in option_text
                ):
                    question_options.append(option)

            except Exception:
                continue

        if not question_options:
            # 如果没找到特定题目的选项，尝试查找当前可见的选项
            print(f"未找到题目 {question_num} 的特定选项，尝试查找当前可见选项")

            # 获取当前视窗内的选项
            visible_options = []
            for option in all_options:
                try:
                    is_visible = await option.is_visible()
                    if is_visible:
                        visible_options.append(option)
                except Exception:
                    continue

            # 如果有可见选项，使用前几个
            if visible_options:
                question_options = visible_options[:4]  # 假设每题最多4个选项

        # 根据答案类型填写
        if question_type == "judge":
            return await self.fill_judge_options(question_options, answer)
        elif question_type == "multiple":
            return await self.fill_multiple_options(question_options, answer)
        else:
            return await self.fill_single_options(question_options, answer)

    async def fill_single_question(self, page, question_info, answer):
        """填写单选题"""
        # 查找选项
        options = await self.find_options_near_question(page, question_info["element"])

        for option in options:
            try:
                option_text = await option.inner_text()
                if option_text.strip().startswith(answer):
                    await option.click()
                    print(f"已选择选项: {option_text.strip()}")
                    return True  # 选择后立即返回，避免重复点击
            except Exception:
                continue
        return False

    async def fill_multiple_question(self, page, question_info, answer):
        """填写多选题"""
        # 查找选项
        options = await self.find_options_near_question(page, question_info["element"])

        selected_options = []
        for option in options:
            try:
                option_text = await option.inner_text()
                option_letter = option_text.strip()[0] if option_text.strip() else ""

                if option_letter in answer and option_letter not in selected_options:
                    await option.click()
                    selected_options.append(option_letter)
                    print(f"已选择选项: {option_text.strip()}")

                    # 如果已选择所有需要的选项，退出循环
                    if len(selected_options) == len(answer):
                        break
            except Exception:
                continue
        return len(selected_options) > 0

    async def fill_judge_question(self, page, question_info, answer):
        """填写判断题"""
        # 查找选项
        options = await self.find_options_near_question(page, question_info["element"])

        target_text = "正确" if answer == "A" else "错误"

        for option in options:
            try:
                option_text = await option.inner_text()
                if target_text in option_text:
                    await option.click()
                    print(f"已选择: {option_text.strip()}")
                    return True  # 选择后立即返回
            except Exception:
                continue
        return False

    async def fill_single_options(self, options, answer):
        """从选项列表中填写单选题"""
        for option in options:
            try:
                option_text = await option.inner_text()
                if option_text.strip().startswith(answer):
                    await option.click()
                    print(f"已选择选项: {option_text.strip()}")
                    return True
            except Exception:
                continue
        return False

    async def fill_multiple_options(self, options, answer):
        """从选项列表中填写多选题"""
        selected_count = 0
        for option in options:
            try:
                option_text = await option.inner_text()
                option_letter = option_text.strip()[0] if option_text.strip() else ""
                if option_letter in answer:
                    await option.click()
                    selected_count += 1
                    print(f"已选择选项: {option_text.strip()}")
            except Exception:
                continue
        return selected_count > 0

    async def fill_judge_options(self, options, answer):
        """从选项列表中填写判断题"""
        target_text = "正确" if answer == "A" else "错误"
        for option in options:
            try:
                option_text = await option.inner_text()
                if target_text in option_text:
                    await option.click()
                    print(f"已选择: {option_text.strip()}")
                    return True
            except Exception:
                continue
        return False

    async def find_options_near_question(self, page, question_element):
        """查找题目附近的选项"""
        # 尝试多种方式查找选项
        selectors = [
            'input[type="radio"]',
            'input[type="checkbox"]',
            ".option",
            '[class*="option"]',
            "label",
            'div:has-text("A")',
            'div:has-text("B")',
            'span:has-text("A")',
            'span:has-text("B")',
        ]

        options = []
        for selector in selectors:
            elements = await page.query_selector_all(selector)
            if elements:
                options.extend(elements)

        return options

    async def submit_form(self, page):
        """提交表单"""
        print("正在查找提交按钮...")

        # 等待一下确保页面加载完成
        await asyncio.sleep(2)

        # 更全面的提交按钮选择器，优先查找"交卷"
        submit_keywords = ["交卷", "提交", "完成", "确认提交", "结束", "submit"]

        # 首先查找所有按钮和可点击元素
        all_clickable = await page.query_selector_all(
            'button, input[type="submit"], input[type="button"], [onclick], .btn, .submit'
        )

        # 优先查找包含"交卷"的按钮
        for element in all_clickable:
            try:
                text = await element.inner_text()
                if "交卷" in text:
                    print(f"找到交卷按钮: {text}")
                    await element.click()
                    return True
            except Exception:
                continue

        # 如果没找到交卷按钮，查找其他提交相关按钮
        for element in all_clickable:
            try:
                text = await element.inner_text()
                if any(keyword in text for keyword in submit_keywords):
                    print(f"找到提交按钮: {text}")
                    await element.click()
                    return True
            except Exception:
                continue

        # 如果没找到，尝试所有按钮
        print("尝试查找所有按钮...")
        all_buttons = await page.query_selector_all(
            'button, input[type="button"], input[type="submit"]'
        )
        for button in all_buttons:
            try:
                text = await button.inner_text()
                print(f"发现按钮: {text}")
                if any(
                    keyword in text
                    for keyword in ["提交", "交卷", "完成", "确认", "结束", "submit"]
                ):
                    print(f"点击提交按钮: {text}")
                    await button.click()
                    return True
            except Exception:
                continue

        print("未找到提交按钮，尝试查看页面内容...")
        # 打印页面内容以便调试
        try:
            page_text = await page.evaluate("document.body.innerText")
            print("页面文本内容:")
            print(page_text[-500:])  # 打印最后500个字符
        except Exception:
            print("无法获取页面文本")

        return False


async def main():
    """主函数"""
    url = (
        "http://wap.xiaoyuananquantong.com/guns-vip-main/wap/newStudentArticle"
        "?id=1493144438782836737&userId=1959537042895429633&ah="
    )

    filler = FormFiller()
    await filler.fill_form(url)


if __name__ == "__main__":
    asyncio.run(main())
